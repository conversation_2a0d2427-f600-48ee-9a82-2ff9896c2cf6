<script lang="ts">
  import { page } from "$app/state";
  import { goto } from "$app/navigation";
  import { getClient } from "$lib/acrpc";
  import { formatDatetime, fetchWithAuth } from "$lib";
  import { Consts } from "@commune/api";
  import MemberCard from "./member-card.svelte";
  import InviteUserModal from "./invite-user-modal.svelte";
  import EditCommuneModal from "./edit-commune-modal.svelte";

  const i18n = {
    en: {
      _page: {
        title: "— Commune",
      },
      loading: "Loading commune details...",
      communeNotFound: "Commune not found",
      communeDetails: "Commune Details",
      edit: "Edit",
      delete: "Delete Commune",
      members: "Members",
      member: "member",
      members_plural: "members",
      headMember: "Head Member",
      created: "Created",
      addMember: "Add Member",
      invite: "Invite Person",
      manageInvitations: "Manage Invitations",
      manageJoinRequests: "Manage Join Requests",
      requestToJoin: "Request to Join",
      requestPending: "Join Request Pending",
      noMembers: "No members found",
      errorFetchingCommune: "Failed to fetch commune",
      errorFetchingMembers: "Failed to fetch members",
      errorOccurred: "An error occurred while fetching data",
      errorSendingJoinRequest: "Failed to send join request",
      joinRequestSent: "Join request sent successfully!",
      sendingJoinRequest: "Sending request...",
      interestedInJoining: "Interested in joining this commune?",
      joinRequestDescription: "Send a join request to the commune head for approval.",
      joinRequestPendingDescription:
        "Your join request is awaiting approval from the commune head.",
      confirmDelete: "Delete Commune",
      confirmDeleteMessage: "Are you sure you want to delete this commune?",
      deleteWarning:
        "This will permanently delete the commune and all its data, including members, invitations, and join requests. This action cannot be undone.",
      deleting: "Deleting...",
      communeDeleted: "Commune deleted successfully",
      errorDeletingCommune: "Failed to delete commune",
      cancel: "Cancel",
      dateFormatLocale: "en-US",
      noImages: "No images available",
      communeImageAlt: "Commune image",
      uploadImage: "Upload Image",
      deleteImage: "Delete Image",
      uploadImageTitle: "Upload Commune Image",
      upload: "Upload",
      uploading: "Uploading...",
      imageUploadedSuccess: "Image uploaded successfully!",
      errorUploadingImage: "Failed to upload image",
      pleaseSelectImage: "Please select an image to upload",
      invalidFileType: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
      fileTooLarge: "File is too large. Maximum size is 5MB.",
      uploadImageMaxSize: "Upload an image (JPG, PNG, WebP), max 5MB.",
      confirmDeleteImage: "Are you sure you want to delete this image?",
      deleteImageTitle: "Delete Image",
      deleteImageButton: "Delete",
      deletingImage: "Deleting...",
      imageDeletedSuccess: "Image deleted successfully!",
      errorDeletingImage: "Failed to delete image",
    },
    ru: {
      _page: {
        title: "— Коммуна",
      },
      loading: "Загрузка данных коммуны...",
      communeNotFound: "Коммуна не найдена",
      communeDetails: "Информация о коммуне",
      edit: "Редактировать",
      delete: "Удалить коммуну",
      members: "Участники",
      member: "участник",
      members_plural: "участников",
      headMember: "Глава",
      created: "Создана",
      addMember: "Добавить участника",
      invite: "Пригласить человека",
      manageInvitations: "Управление приглашениями",
      manageJoinRequests: "Управление заявками",
      requestToJoin: "Подать заявку",
      requestPending: "Заявка на рассмотрении",
      noMembers: "Участники не найдены",
      errorFetchingCommune: "Не удалось загрузить коммуну",
      errorFetchingMembers: "Не удалось загрузить участников",
      errorOccurred: "Произошла ошибка при загрузке данных",
      errorSendingJoinRequest: "Не удалось отправить заявку",
      joinRequestSent: "Заявка отправлена успешно!",
      sendingJoinRequest: "Отправляем заявку...",
      interestedInJoining: "Хотите присоединиться к этой коммуне?",
      joinRequestDescription: "Отправьте заявку главе коммуны для одобрения.",
      joinRequestPendingDescription: "Ваша заявка ожидает одобрения главы коммуны.",
      confirmDelete: "Удалить коммуну",
      confirmDeleteMessage: "Вы уверены, что хотите удалить эту коммуну?",
      deleteWarning:
        "Это навсегда удалит коммуну и все её данные, включая участников, приглашения и заявки. Это действие нельзя отменить.",
      deleting: "Удаление...",
      communeDeleted: "Коммуна успешно удалена",
      errorDeletingCommune: "Не удалось удалить коммуну",
      cancel: "Отмена",
      dateFormatLocale: "ru-RU",
      noImages: "Нет доступных изображений",
      communeImageAlt: "Изображение коммуны",
      uploadImage: "Загрузить изображение",
      deleteImage: "Удалить изображение",
      uploadImageTitle: "Загрузить изображение коммуны",
      upload: "Загрузить",
      uploading: "Загрузка...",
      imageUploadedSuccess: "Изображение загружено успешно!",
      errorUploadingImage: "Не удалось загрузить изображение",
      pleaseSelectImage: "Пожалуйста, выберите изображение для загрузки",
      invalidFileType: "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
      fileTooLarge: "Файл слишком большой. Максимальный размер - 5MB.",
      uploadImageMaxSize: "Загрузите изображение (JPG, PNG, WebP), максимальный размер - 5MB.",
      confirmDeleteImage: "Вы уверены, что хотите удалить это изображение?",
      deleteImageTitle: "Удалить изображение",
      deleteImageButton: "Удалить",
      deletingImage: "Удаление...",
      imageDeletedSuccess: "Изображение удалено успешно!",
      errorDeletingImage: "Не удалось удалить изображение",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const communeId = page.params.id;

  const t = $derived(i18n[locale]);

  const user = $derived(data.user);

  let commune = $state(data.commune);
  let members = $state(data.members);
  let error = $state<string | null>(null);
  let showEditModal = $state(false);
  let showInviteModal = $state(false);
  let showDeleteModal = $state(false);
  let isSendingJoinRequest = $state(false);
  let isDeleting = $state(false);

  // Image upload modal state
  let showUploadModal = $state(false);
  let isUploading = $state(false);
  let uploadError = $state<string | null>(null);
  let uploadSuccess = $state<string | null>(null);
  let selectedFile = $state<File | null>(null);
  let previewUrl = $state<string | null>(null);

  // Image delete modal state
  let showDeleteImageModal = $state(false);
  let isDeletingImage = $state(false);
  let deleteImageError = $state<string | null>(null);
  let deleteImageSuccess = $state<string | null>(null);

  // Get user permissions from page data
  const userPermissions = $derived(data.userPermissions);

  function refresh() {
    window.location.reload();
  }

  function handleEditClick() {
    showEditModal = true;
  }

  function handleDeleteClick() {
    showDeleteModal = true;
  }

  function handleEditModalClose() {
    showEditModal = false;
    refresh();
  }

  const communeName = $derived(getAppropriateLocalization(commune.name));
  const communeDescription = $derived(getAppropriateLocalization(commune.description));

  const isCurrentUserHead = $derived(
    user
      ? commune.headMember.actorType === "user" && commune.headMember.actorId === user.id
      : false,
  );

  // Invite modal handlers
  function handleInviteClick() {
    showInviteModal = true;
  }

  function handleInviteModalClose() {
    showInviteModal = false;
  }

  function handleInviteSent() {
    // Optionally refresh members or show success message
    console.log("Invitation sent successfully");
  }

  // Join request handler
  async function handleJoinRequest() {
    if (!user) return;

    isSendingJoinRequest = true;
    error = null;

    try {
      await api.commune.joinRequest.post({
        communeId,
        userId: user.id,
      });

      alert(t.joinRequestSent);

      refresh();
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorSendingJoinRequest;
      console.error("Error sending join request:", err);
    } finally {
      isSendingJoinRequest = false;
    }
  }

  async function handleConfirmDelete() {
    isDeleting = true;
    error = null;

    try {
      await api.commune.delete({ id: communeId });

      alert(t.communeDeleted);

      goto(toLocaleHref("/communes"));
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorDeletingCommune;
      console.error("Error deleting commune:", err);
    } finally {
      isDeleting = false;
    }
  }

  function closeDeleteModal() {
    showDeleteModal = false;
  }

  // Image upload functions
  function openUploadModal() {
    showUploadModal = true;
    resetUploadForm();
  }

  function closeUploadModal() {
    showUploadModal = false;
    selectedFile = null;
    previewUrl = null;
  }

  function resetUploadForm() {
    uploadError = null;
    uploadSuccess = null;
    isUploading = false;
    selectedFile = null;
    previewUrl = null;
  }

  function handleFileChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const files = target.files;

    uploadError = null;

    if (!files || files.length === 0) {
      selectedFile = null;
      previewUrl = null;
      return;
    }

    const file = files[0];

    // Validate file type
    if (!Consts.ALLOWED_IMAGE_FILE_TYPES.includes(file.type)) {
      uploadError = t.invalidFileType;
      selectedFile = null;
      previewUrl = null;
      // Clear the input immediately if validation fails
      target.value = "";
      return;
    }

    // Validate file size
    if (file.size > Consts.MAX_IMAGE_FILE_SIZE) {
      uploadError = t.fileTooLarge;
      selectedFile = null;
      previewUrl = null;
      // Clear the input immediately if validation fails
      target.value = "";
      return;
    }

    selectedFile = file;

    // Create preview URL
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    previewUrl = URL.createObjectURL(file);
  }

  async function handleUploadImage() {
    if (!selectedFile) {
      uploadError = t.pleaseSelectImage;
      return;
    }

    isUploading = true;
    uploadError = null;
    uploadSuccess = null;

    try {
      const formData = new FormData();
      formData.append("image", selectedFile);

      const response = await fetchWithAuth(`/api/commune/${communeId}/image`, {
        method: "PUT",
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`${t.errorUploadingImage}: ${response.statusText}`);
      }

      uploadSuccess = t.imageUploadedSuccess;

      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (err) {
      uploadError = err instanceof Error ? err.message : t.errorUploadingImage;
      console.error(err);
    } finally {
      isUploading = false;
    }
  }

  // Image delete functions
  function openDeleteImageModal() {
    showDeleteImageModal = true;
    resetDeleteImageForm();
  }

  function closeDeleteImageModal() {
    showDeleteImageModal = false;
  }

  function resetDeleteImageForm() {
    deleteImageError = null;
    deleteImageSuccess = null;
    isDeletingImage = false;
  }

  async function handleDeleteImage() {
    isDeletingImage = true;
    deleteImageError = null;
    deleteImageSuccess = null;

    try {
      const response = await fetchWithAuth(`/api/commune/${communeId}/image`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error(`${t.errorDeletingImage}: ${response.statusText}`);
      }

      deleteImageSuccess = t.imageDeletedSuccess;

      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (err) {
      deleteImageError = err instanceof Error ? err.message : t.errorDeletingImage;
      console.error(err);
    } finally {
      isDeletingImage = false;
    }
  }
</script>

<svelte:head>
  <title>{communeName} {t._page.title}</title>
</svelte:head>

<div class="container py-4">
  {#if error}
    <div class="alert alert-danger">
      {error}
    </div>
  {:else}
    <div class="row">
      <div class="col-lg-8">
        <div class="commune-image-container">
          {#if commune.image}
            <img
              src={`/images/${commune.image}`}
              alt={`${t.communeImageAlt}`}
              class="commune-image"
            />
          {:else}
            <div class="commune-image-placeholder">
              <span class="text-muted">{t.noImages}</span>
            </div>
          {/if}
        </div>

        <!-- Image Management Buttons -->
        {#if isCurrentUserHead || userPermissions.isAdmin}
          <div class="mt-3 d-flex gap-2">
            <button class="btn btn-outline-primary btn-sm" onclick={openUploadModal}>
              <i class="bi bi-upload me-1"></i>
              {t.uploadImage}
            </button>
            {#if commune.image}
              <button class="btn btn-outline-danger btn-sm" onclick={openDeleteImageModal}>
                <i class="bi bi-trash me-1"></i>
                {t.deleteImage}
              </button>
            {/if}
          </div>
        {/if}

        <!-- Commune Information -->
        <div class="mb-4">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="mb-0">{communeName}</h2>
          </div>
          <p class="lead text-muted">{communeDescription || ""}</p>
        </div>
      </div>

      <div class="col-lg-4">
        <div class="card shadow-sm mb-4">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h5 class="card-title mb-0">{t.communeDetails}</h5>
              {#if isCurrentUserHead || userPermissions.isAdmin}
                <div class="d-flex gap-1">
                  <button class="btn btn-outline-primary btn-sm" onclick={handleEditClick}>
                    {t.edit}
                  </button>
                  <button class="btn btn-outline-danger btn-sm" onclick={handleDeleteClick}>
                    {t.delete}
                  </button>
                </div>
              {/if}
            </div>
            <hr />
            <div class="d-flex justify-content-between mb-2">
              <span>{t.members}:</span>
              <span class="badge bg-primary">{commune.memberCount}</span>
            </div>
            <div class="d-flex justify-content-between mb-2">
              <span>{t.headMember}:</span>
              <span class="text-muted">
                {getAppropriateLocalization(commune.headMember.name)}
              </span>
            </div>
            <div class="d-flex justify-content-between">
              <span>{t.created}:</span>
              <span class="text-muted">
                {formatDatetime(new Date(commune.createdAt), locale)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Join Request Section (for non-members) -->
    {#if userPermissions.canRequestJoin}
      <div class="alert alert-info d-flex justify-content-between align-items-center mt-4">
        <div>
          <strong>{t.interestedInJoining}</strong>
          <p class="mb-0 small text-muted">{t.joinRequestDescription}</p>
        </div>
        <button class="btn btn-success" onclick={handleJoinRequest} disabled={isSendingJoinRequest}>
          {#if isSendingJoinRequest}
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            {t.sendingJoinRequest}
          {:else}
            {t.requestToJoin}
          {/if}
        </button>
      </div>
    {:else if userPermissions.hasPendingJoinRequest}
      <div class="alert alert-warning mt-4">
        <strong>{t.requestPending}</strong>
        <p class="mb-0 small">{t.joinRequestPendingDescription}</p>
      </div>
    {/if}

    <!-- Members Section -->
    <div class="mt-5 mb-4">
      <h3 class="mb-3">{t.members} ({members.length})</h3>
      {#if userPermissions.canInvite}
        <div class="d-flex gap-2 flex-wrap mb-3">
          <a href={toLocaleHref(`/communes/${communeId}/invitations`)} class="btn btn-outline-info">
            {t.manageInvitations}
          </a>
          <a
            href={toLocaleHref(`/communes/${communeId}/join-requests`)}
            class="btn btn-outline-success"
          >
            {t.manageJoinRequests}
          </a>
          <button class="btn btn-outline-primary" onclick={handleInviteClick}>
            {t.invite}
          </button>
          <!-- {#if isCurrentUserHead}
            <button class="btn btn-primary" onclick={handleAddMemberClick}>
              {t.addMember}
            </button>
          {/if} -->
        </div>
      {/if}
    </div>

    {#if members.length === 0}
      <div class="alert alert-info">{t.noMembers}</div>
    {:else}
      <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4">
        {#each members as member (member.id)}
          <div class="col">
            <MemberCard
              id={member.id}
              actorType={member.actorType}
              actorId={member.actorId}
              name={member.name ?? []}
              isHead={member.actorType === commune.headMember.actorType &&
                member.actorId === commune.headMember.actorId}
              createdAt={member.createdAt}
              {communeId}
              {isCurrentUserHead}
              isCurrentUserAdmin={userPermissions.isAdmin}
              {locale}
              onMemberRemoved={refresh}
              onHeadTransferred={refresh}
              image={member.image}
              {toLocaleHref}
              {getAppropriateLocalization}
            />
          </div>
        {/each}
      </div>
    {/if}

    <!-- Edit Commune Modal -->
    <EditCommuneModal
      show={showEditModal}
      onHide={handleEditModalClose}
      communeData={commune}
      {locale}
      onCommuneUpdated={refresh}
    />

    <!-- Invite User Modal -->
    <InviteUserModal
      show={showInviteModal}
      onHide={handleInviteModalClose}
      {communeId}
      {locale}
      onInviteSent={handleInviteSent}
    />
  {/if}
</div>

<!-- Upload Image Modal -->
{#if isCurrentUserHead || userPermissions.isAdmin}
  <!-- svelte-ignore a11y_click_events_have_key_events -->
  <!-- svelte-ignore a11y_no_static_element_interactions -->
  {#if showUploadModal}
    <div
      class="modal fade show"
      style="display: block; background-color: rgba(0,0,0,0.5);"
      tabindex="-1"
      aria-modal="true"
      role="dialog"
      onclick={closeUploadModal}
      onkeydown={(e) => e.key === "Escape" && closeUploadModal()}
    >
      <!-- svelte-ignore a11y_click_events_have_key_events -->
      <!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
      <div
        class="modal-dialog modal-dialog-centered modal-lg"
        role="document"
        onclick={(e) => e.stopPropagation()}
      >
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{t.uploadImageTitle}</h5>
            <button type="button" class="btn-close" aria-label="Close" onclick={closeUploadModal}
            ></button>
          </div>
          <div class="modal-body">
            {#if uploadSuccess}
              <div class="alert alert-success mb-3">
                {uploadSuccess}
              </div>
            {/if}

            {#if uploadError}
              <div class="alert alert-danger mb-3">
                {uploadError}
              </div>
            {/if}

            <form>
              <div class="mb-3">
                <label for="imageInput" class="form-label">{t.pleaseSelectImage}</label>
                <input
                  id="imageInput"
                  type="file"
                  class="form-control"
                  accept=".jpg,.jpeg,.png,.webp"
                  onchange={handleFileChange}
                  disabled={isUploading}
                />
                <p class="form-text text-muted">
                  {t.uploadImageMaxSize}
                </p>

                {#if previewUrl}
                  <div class="mt-3 text-center">
                    <img
                      src={previewUrl}
                      alt="Preview"
                      class="img-thumbnail"
                      style:max-height="200px"
                    />
                  </div>
                {/if}
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick={closeUploadModal}>
              {t.cancel}
            </button>
            <button
              type="button"
              class="btn btn-primary"
              onclick={handleUploadImage}
              disabled={!selectedFile || isUploading}
            >
              {#if isUploading}
                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                {t.uploading}
              {:else}
                {t.upload}
              {/if}
            </button>
          </div>
        </div>
      </div>
    </div>
  {/if}
{/if}

<!-- Delete Image Modal -->
{#if (isCurrentUserHead || userPermissions.isAdmin) && commune.image}
  <!-- svelte-ignore a11y_click_events_have_key_events -->
  <!-- svelte-ignore a11y_no_static_element_interactions -->
  {#if showDeleteImageModal}
    <div
      class="modal fade show"
      style="display: block; background-color: rgba(0,0,0,0.5);"
      tabindex="-1"
      aria-modal="true"
      role="dialog"
      onclick={closeDeleteImageModal}
      onkeydown={(e) => e.key === "Escape" && closeDeleteImageModal()}
    >
      <!-- svelte-ignore a11y_click_events_have_key_events -->
      <!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
      <div
        class="modal-dialog modal-dialog-centered"
        role="document"
        onclick={(e) => e.stopPropagation()}
      >
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{t.deleteImageTitle}</h5>
            <button
              type="button"
              class="btn-close"
              aria-label="Close"
              onclick={closeDeleteImageModal}
            ></button>
          </div>
          <div class="modal-body">
            {#if deleteImageSuccess}
              <div class="alert alert-success mb-3">
                {deleteImageSuccess}
              </div>
            {/if}

            {#if deleteImageError}
              <div class="alert alert-danger mb-3">
                {deleteImageError}
              </div>
            {/if}

            <p>{t.confirmDeleteImage}</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick={closeDeleteImageModal}>
              {t.cancel}
            </button>
            <button
              type="button"
              class="btn btn-danger"
              onclick={handleDeleteImage}
              disabled={isDeletingImage}
            >
              {#if isDeletingImage}
                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                {t.deletingImage}
              {:else}
                {t.deleteImageButton}
              {/if}
            </button>
          </div>
        </div>
      </div>
    </div>
  {/if}
{/if}

<!-- Delete Confirmation Modal -->
{#if showDeleteModal}
  <!-- svelte-ignore a11y_click_events_have_key_events -->
  <!-- svelte-ignore a11y_no_static_element_interactions -->
  <div
    class="modal fade show"
    style="display: block; background-color: rgba(0,0,0,0.5);"
    tabindex="-1"
    aria-modal="true"
    role="dialog"
    onclick={closeDeleteModal}
    onkeydown={(e) => e.key === "Escape" && closeDeleteModal()}
  >
    <!-- svelte-ignore a11y_click_events_have_key_events -->
    <!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
    <div
      class="modal-dialog modal-dialog-centered"
      role="document"
      onclick={(e) => e.stopPropagation()}
    >
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{t.confirmDelete}</h5>
          <button type="button" class="btn-close" aria-label="Close" onclick={closeDeleteModal}
          ></button>
        </div>
        <div class="modal-body">
          {#if error}
            <div class="alert alert-danger" role="alert">
              {error}
            </div>
          {/if}
          <p>
            {t.confirmDeleteMessage}
          </p>
          <div class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {t.deleteWarning}
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" onclick={closeDeleteModal}>
            {t.cancel}
          </button>
          <button
            type="button"
            class="btn btn-danger"
            onclick={handleConfirmDelete}
            disabled={isDeleting}
          >
            {#if isDeleting}
              <span class="spinner-border spinner-border-sm me-2" role="status"></span>
              {t.deleting}
            {:else}
              {t.confirmDelete}
            {/if}
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .commune-image-container {
    width: 100%;
    height: 300px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
  }

  .commune-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .commune-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
  }

  @media (max-width: 768px) {
    .commune-image-container {
      height: 250px;
    }
  }

  @media (max-width: 576px) {
    .commune-image-container {
      height: 200px;
    }
  }
</style>
